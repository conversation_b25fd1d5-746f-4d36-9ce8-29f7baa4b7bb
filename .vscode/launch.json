{"version": "0.2.0", "configurations": [{"type": "java", "name": "Spring Boot-HMallApplication<hm-service>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.hmall.HMallApplication", "projectName": "hm-service", "args": "--spring.profiles.active=local", "envFile": "${workspaceFolder}/.env"}, {"type": "java", "name": "启动商品服务 (hm-product-service)", "request": "launch", "cwd": "${workspaceFolder}/hmall", "mainClass": "com.hmall.product.ProductApplication", "projectName": "hm-product-service", "args": "--spring.profiles.active=local", "vmArgs": "-Dspring.profiles.active=local", "env": {}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"type": "java", "name": "启动购物车服务 (hm-cart-service)", "request": "launch", "cwd": "${workspaceFolder}/hmall", "mainClass": "com.hmall.cart.CartApplication", "projectName": "hm-cart-service", "args": "--spring.profiles.active=local", "vmArgs": "-Dspring.profiles.active=local", "env": {}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}]}