{"version": "2.0.0", "tasks": [{"label": "启动商品服务", "type": "shell", "command": "mvn", "args": ["spring-boot:run", "-Dspring-boot.run.profiles=local"], "options": {"cwd": "${workspaceFolder}/hmall/hm-product-service"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "启动购物车服务", "type": "shell", "command": "mvn", "args": ["spring-boot:run", "-Dspring-boot.run.profiles=local"], "options": {"cwd": "${workspaceFolder}/hmall/hm-cart-service"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "启动所有微服务", "dependsOrder": "sequence", "dependsOn": ["启动商品服务", "启动购物车服务"], "group": "build"}]}