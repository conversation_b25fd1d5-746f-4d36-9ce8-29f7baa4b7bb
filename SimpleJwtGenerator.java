import java.util.Base64;
import java.nio.charset.StandardCharsets;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.Instant;

public class SimpleJwtGenerator {
    public static void main(String[] args) {
        try {
            // JWT Header
            String header = "{\"alg\":\"HS256\",\"typ\":\"JWT\"}";
            String encodedHeader = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(header.getBytes(StandardCharsets.UTF_8));
            
            // JWT Payload (用户ID为1，过期时间为当前时间+30分钟)
            long exp = Instant.now().getEpochSecond() + 1800; // 30分钟后过期
            String payload = "{\"user\":1,\"exp\":" + exp + "}";
            String encodedPayload = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(payload.getBytes(StandardCharsets.UTF_8));
            
            // 创建签名
            String data = encodedHeader + "." + encodedPayload;
            String secret = "test-secret-key"; // 简单的密钥
            
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String encodedSignature = Base64.getUrlEncoder().withoutPadding().encodeToString(signature);
            
            // 完整的JWT token
            String token = data + "." + encodedSignature;
            
            System.out.println("Generated Simple JWT Token:");
            System.out.println(token);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
