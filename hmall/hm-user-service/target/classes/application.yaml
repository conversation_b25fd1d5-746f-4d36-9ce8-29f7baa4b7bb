server:
  port: 8083
spring:
  application:
    name: hm-user-service
  profiles:
    active: local
  datasource:
    url: jdbc:mysql://${hm.db.host}:${hm.db.port:3306}/hm-user?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: ${hm.db.pw}
  cloud:
    nacos:
      discovery:
        server-addr: ${hm.nacos.addr}
        namespace: ${hm.nacos.namespace}
      config:
        server-addr: ${hm.nacos.addr}
        namespace: ${hm.nacos.namespace}
        file-extension: yaml
mybatis-plus:
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      update-strategy: NOT_NULL
      id-type: auto
logging:
  level:
    "[com.hmall]": debug
  pattern:
    dateformat: HH:mm:ss:SSS
  file:
    path: "logs/${spring.application.name}"
knife4j:
  enable: true
  openapi:
    title: 用户服务接口文档
    description: "黑马商城用户服务接口文档"
    email: <EMAIL>
    concat: 虎哥
    url: https://www.itcast.cn
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.hmall.user.controller
hm:
  jwt:
    location: classpath:hmall.jks
    alias: hmall
    password: hmall123
    tokenTTL: 30m
  auth:
    excludePaths:
      - /users/login
      - /addresses/**
