<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.hmall.service.integration.MicroserviceIntegrationTest" time="5.699" tests="6" errors="5" skipped="0" failures="1">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/test-classes:/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/classes:/home/<USER>/.m2/repository/com/heima/hm-common/1.0.0/hm-common-1.0.0.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar:/home/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.11/hutool-all-5.8.11.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.12/spring-boot-starter-logging-2.7.12.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.12/spring-boot-autoconfigure-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.12/spring-boot-2.7.12.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi2-spring-boot-starter/4.1.0/knife4j-openapi2-spring-boot-starter-4.1.0.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-core/4.1.0/knife4j-core-4.1.0.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi2-ui/4.1.0/knife4j-openapi2-ui-4.1.0.jar:/home/<USER>/.m2/repository/org/javassist/javassist/3.25.0-GA/javassist-3.25.0-GA.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.10.5/springfox-swagger2-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spi/2.10.5/springfox-spi-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-core/2.10.5/springfox-core-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-schema/2.10.5/springfox-schema-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.10.5/springfox-swagger-common-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.10.5/springfox-spring-web-2.10.5.jar:/home/<USER>/.m2/repository/io/github/classgraph/classgraph/4.1.7/classgraph-4.1.7.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/home/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/home/<USER>/.m2/repository/io/swagger/swagger-models/1.6.6/swagger-models-1.6.6.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/home/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.6/swagger-annotations-1.6.6.jar:/home/<USER>/.m2/repository/io/springfox/springfox-bean-validators/2.10.5/springfox-bean-validators-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/2.10.5/springfox-spring-webmvc-2.10.5.jar:/home/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.9.3/caffeine-2.9.3.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.19.0/checker-qual-3.19.0.jar:/home/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.10.0/error_prone_annotations-2.10.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.12/spring-boot-starter-web-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.12/spring-boot-starter-2.7.12.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.12/spring-boot-starter-json-2.7.12.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.12/spring-boot-starter-tomcat-2.7.12.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.75/tomcat-embed-core-9.0.75.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.75/tomcat-embed-el-9.0.75.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.75/tomcat-embed-websocket-9.0.75.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/5.3.27/spring-web-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/5.3.27/spring-beans-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.27/spring-webmvc-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/5.3.27/spring-aop-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/5.3.27/spring-context-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/5.3.27/spring-expression-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.7.8/spring-security-crypto-5.7.8.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.0.9.RELEASE/spring-security-rsa-1.0.9.RELEASE.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.64/bcpkix-jdk15on-1.64.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar:/home/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar:/home/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.4.3/mybatis-plus-boot-starter-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.4.3/mybatis-plus-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.4.3/mybatis-plus-extension-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.4.3/mybatis-plus-core-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.4.3/mybatis-plus-annotation-3.4.3.jar:/home/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.0/jsqlparser-4.0.jar:/home/<USER>/.m2/repository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar:/home/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.12/spring-boot-starter-jdbc-2.7.12.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.27/spring-jdbc-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.12/spring-boot-starter-test-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.12/spring-boot-test-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.12/spring-boot-test-autoconfigure-2.7.12.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/5.3.27/spring-core-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.27/spring-jcl-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/5.3.27/spring-test-5.3.27.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.7.12/spring-boot-starter-data-redis-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.7.12/spring-data-redis-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.7.12/spring-data-keyvalue-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.12/spring-data-commons-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/5.3.27/spring-tx-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.27/spring-oxm-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.27/spring-context-support-5.3.27.jar:/home/<USER>/.m2/repository/io/lettuce/lettuce-core/6.1.10.RELEASE/lettuce-core-6.1.10.RELEASE.jar:/home/<USER>/.m2/repository/io/netty/netty-common/4.1.92.Final/netty-common-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-handler/4.1.92.Final/netty-handler-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-resolver/4.1.92.Final/netty-resolver-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-buffer/4.1.92.Final/netty-buffer-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.92.Final/netty-transport-native-unix-common-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-codec/4.1.92.Final/netty-codec-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-transport/4.1.92.Final/netty-transport-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.29/reactor-core-3.4.29.jar:/home/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2021.0.4.0/spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2021.0.4.0/spring-cloud-alibaba-commons-2021.0.4.0.jar:/home/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/home/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/home/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient/0.15.0/simpleclient-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.15.0/simpleclient_tracer_otel-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.15.0/simpleclient_tracer_common-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.15.0/simpleclient_tracer_otel_agent-0.15.0.jar:/home/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/3.1.3/spring-cloud-commons-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/3.1.3/spring-cloud-context-3.1.3.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2021.0.4.0/spring-cloud-starter-alibaba-nacos-config-2021.0.4.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/3.1.3/spring-cloud-starter-openfeign-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/3.1.3/spring-cloud-starter-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/3.1.3/spring-cloud-openfeign-core-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.7.12/spring-boot-starter-aop-2.7.12.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.2/commons-io-2.2.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-core/11.8/feign-core-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/11.8/feign-slf4j-11.8.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/3.1.3/spring-cloud-starter-loadbalancer-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-loadbalancer/3.1.3/spring-cloud-loadbalancer-3.1.3.jar:/home/<USER>/.m2/repository/io/projectreactor/addons/reactor-extra/3.4.10/reactor-extra-3.4.10.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/2.7.12/spring-boot-starter-cache-2.7.12.jar:/home/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evictor-1.0.0.jar:/home/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/home/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:"/>
    <property name="java.vm.vendor" value="Ubuntu"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://ubuntu.com/"/>
    <property name="os.name" value="Linux"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/usr/lib/jvm/java-17-openjdk-amd64/lib"/>
    <property name="sun.java.command" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/surefire/surefirebooter15742822995085738310.jar /home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/surefire 2025-07-31T14-14-38_632-jvmRun1 surefire11435234451558537223tmp surefire_016839048710830762748tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/test-classes:/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/classes:/home/<USER>/.m2/repository/com/heima/hm-common/1.0.0/hm-common-1.0.0.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar:/home/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.11/hutool-all-5.8.11.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.12/spring-boot-starter-logging-2.7.12.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.12/spring-boot-autoconfigure-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.12/spring-boot-2.7.12.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi2-spring-boot-starter/4.1.0/knife4j-openapi2-spring-boot-starter-4.1.0.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-core/4.1.0/knife4j-core-4.1.0.jar:/home/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi2-ui/4.1.0/knife4j-openapi2-ui-4.1.0.jar:/home/<USER>/.m2/repository/org/javassist/javassist/3.25.0-GA/javassist-3.25.0-GA.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.10.5/springfox-swagger2-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spi/2.10.5/springfox-spi-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-core/2.10.5/springfox-core-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-schema/2.10.5/springfox-schema-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.10.5/springfox-swagger-common-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.10.5/springfox-spring-web-2.10.5.jar:/home/<USER>/.m2/repository/io/github/classgraph/classgraph/4.1.7/classgraph-4.1.7.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/2.0.0.RELEASE/spring-plugin-core-2.0.0.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/2.0.0.RELEASE/spring-plugin-metadata-2.0.0.RELEASE.jar:/home/<USER>/.m2/repository/org/mapstruct/mapstruct/1.3.1.Final/mapstruct-1.3.1.Final.jar:/home/<USER>/.m2/repository/io/swagger/swagger-models/1.6.6/swagger-models-1.6.6.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/home/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.6/swagger-annotations-1.6.6.jar:/home/<USER>/.m2/repository/io/springfox/springfox-bean-validators/2.10.5/springfox-bean-validators-2.10.5.jar:/home/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/2.10.5/springfox-spring-webmvc-2.10.5.jar:/home/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.9.3/caffeine-2.9.3.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.19.0/checker-qual-3.19.0.jar:/home/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.10.0/error_prone_annotations-2.10.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.12/spring-boot-starter-web-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.12/spring-boot-starter-2.7.12.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.12/spring-boot-starter-json-2.7.12.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.12/spring-boot-starter-tomcat-2.7.12.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.75/tomcat-embed-core-9.0.75.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.75/tomcat-embed-el-9.0.75.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.75/tomcat-embed-websocket-9.0.75.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/5.3.27/spring-web-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/5.3.27/spring-beans-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.27/spring-webmvc-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/5.3.27/spring-aop-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/5.3.27/spring-context-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/5.3.27/spring-expression-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.7.8/spring-security-crypto-5.7.8.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.0.9.RELEASE/spring-security-rsa-1.0.9.RELEASE.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.64/bcpkix-jdk15on-1.64.jar:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar:/home/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.23/mysql-connector-java-8.0.23.jar:/home/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.11.4/protobuf-java-3.11.4.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.4.3/mybatis-plus-boot-starter-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.4.3/mybatis-plus-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.4.3/mybatis-plus-extension-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.4.3/mybatis-plus-core-3.4.3.jar:/home/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.4.3/mybatis-plus-annotation-3.4.3.jar:/home/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.0/jsqlparser-4.0.jar:/home/<USER>/.m2/repository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar:/home/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.12/spring-boot-starter-jdbc-2.7.12.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.27/spring-jdbc-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.12/spring-boot-starter-test-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.12/spring-boot-test-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.12/spring-boot-test-autoconfigure-2.7.12.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/5.3.27/spring-core-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.27/spring-jcl-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/5.3.27/spring-test-5.3.27.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.7.12/spring-boot-starter-data-redis-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.7.12/spring-data-redis-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.7.12/spring-data-keyvalue-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.12/spring-data-commons-2.7.12.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/5.3.27/spring-tx-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.27/spring-oxm-5.3.27.jar:/home/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.27/spring-context-support-5.3.27.jar:/home/<USER>/.m2/repository/io/lettuce/lettuce-core/6.1.10.RELEASE/lettuce-core-6.1.10.RELEASE.jar:/home/<USER>/.m2/repository/io/netty/netty-common/4.1.92.Final/netty-common-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-handler/4.1.92.Final/netty-handler-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-resolver/4.1.92.Final/netty-resolver-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-buffer/4.1.92.Final/netty-buffer-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.92.Final/netty-transport-native-unix-common-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-codec/4.1.92.Final/netty-codec-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/netty/netty-transport/4.1.92.Final/netty-transport-4.1.92.Final.jar:/home/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.29/reactor-core-3.4.29.jar:/home/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2021.0.4.0/spring-cloud-starter-alibaba-nacos-discovery-2021.0.4.0.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-alibaba-commons/2021.0.4.0/spring-cloud-alibaba-commons-2021.0.4.0.jar:/home/<USER>/.m2/repository/com/alibaba/nacos/nacos-client/2.0.4/nacos-client-2.0.4.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/home/<USER>/.m2/repository/org/reflections/reflections/0.9.11/reflections-0.9.11.jar:/home/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient/0.15.0/simpleclient-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.15.0/simpleclient_tracer_otel-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.15.0/simpleclient_tracer_common-0.15.0.jar:/home/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.15.0/simpleclient_tracer_otel_agent-0.15.0.jar:/home/<USER>/.m2/repository/com/alibaba/spring/spring-context-support/1.0.11/spring-context-support-1.0.11.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/3.1.3/spring-cloud-commons-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/3.1.3/spring-cloud-context-3.1.3.jar:/home/<USER>/.m2/repository/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-config/2021.0.4.0/spring-cloud-starter-alibaba-nacos-config-2021.0.4.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-openfeign/3.1.3/spring-cloud-starter-openfeign-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/3.1.3/spring-cloud-starter-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-openfeign-core/3.1.3/spring-cloud-openfeign-core-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.7.12/spring-boot-starter-aop-2.7.12.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form-spring/3.8.0/feign-form-spring-3.8.0.jar:/home/<USER>/.m2/repository/io/github/openfeign/form/feign-form/3.8.0/feign-form-3.8.0.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.2/commons-io-2.2.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-core/11.8/feign-core-11.8.jar:/home/<USER>/.m2/repository/io/github/openfeign/feign-slf4j/11.8/feign-slf4j-11.8.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/3.1.3/spring-cloud-starter-loadbalancer-3.1.3.jar:/home/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-loadbalancer/3.1.3/spring-cloud-loadbalancer-3.1.3.jar:/home/<USER>/.m2/repository/io/projectreactor/addons/reactor-extra/3.4.10/reactor-extra-3.4.10.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/2.7.12/spring-boot-starter-cache-2.7.12.jar:/home/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evictor-1.0.0.jar:/home/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/home/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/home/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/usr/lib/jvm/java-17-openjdk-amd64"/>
    <property name="spring.profiles.active" value="local"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/surefire/surefirebooter15742822995085738310.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.15+6-Ubuntu-0ubuntu124.04"/>
    <property name="user.name" value="jiang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="6.6.87.2-microsoft-standard-WSL2"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/home/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugs.launchpad.net/ubuntu/+source/openjdk-17"/>
    <property name="java.io.tmpdir" value="/tmp"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/usr/java/packages/lib:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Ubuntu"/>
    <property name="java.vm.version" value="17.0.15+6-Ubuntu-0ubuntu124.04"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testOrderServiceCreateOrderWithMicroservices" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.314">
    <error message="[503] during [GET] to [http://hm-product-service/items?ids=317578&amp;ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]" type="feign.FeignException$ServiceUnavailable"><![CDATA[feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testOrderServiceCreateOrderWithMicroservices(MicroserviceIntegrationTest.java:139)
]]></error>
    <system-out><![CDATA[14:14:39.390 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating CacheAwareContextLoaderDelegate from class [org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate]
14:14:39.396 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating BootstrapContext using constructor [public org.springframework.test.context.support.DefaultBootstrapContext(java.lang.Class,org.springframework.test.context.CacheAwareContextLoaderDelegate)]
14:14:39.427 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating TestContextBootstrapper for test class [com.hmall.service.integration.MicroserviceIntegrationTest] from class [org.springframework.boot.test.context.SpringBootTestContextBootstrapper]
14:14:39.436 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.hmall.service.integration.MicroserviceIntegrationTest], using SpringBootContextLoader
14:14:39.439 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.hmall.service.integration.MicroserviceIntegrationTest]: class path resource [com/hmall/service/integration/MicroserviceIntegrationTest-context.xml] does not exist
14:14:39.439 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.hmall.service.integration.MicroserviceIntegrationTest]: class path resource [com/hmall/service/integration/MicroserviceIntegrationTestContext.groovy] does not exist
14:14:39.439 [main] INFO org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.hmall.service.integration.MicroserviceIntegrationTest]: no resource found for suffixes {-context.xml, Context.groovy}.
14:14:39.439 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.hmall.service.integration.MicroserviceIntegrationTest]: MicroserviceIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
14:14:39.522 [main] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/classes/com/hmall/HMallApplication.class]
14:14:39.522 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.hmall.HMallApplication for test class com.hmall.service.integration.MicroserviceIntegrationTest
14:14:39.579 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - @TestExecutionListeners is not present for class [com.hmall.service.integration.MicroserviceIntegrationTest]: using defaults.
14:14:39.580 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
14:14:39.590 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@4ff4357f, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@49cb9cb5, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@55322aab, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2b4c1d96, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@45fd9a4d, org.springframework.test.context.support.DirtiesContextTestExecutionListener@50468873, org.springframework.test.context.transaction.TransactionalTestExecutionListener@146587a2, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5f0e9815, org.springframework.test.context.event.EventPublishingTestExecutionListener@76884e4b, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@126945f9, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@2a898881, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@16c63f5, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@35229f85, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@6d3c5255, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@b1712f3]
14:14:39.593 [main] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = [null], testMethod = [null], testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true]], class annotated with @DirtiesContext [false] with mode [null].

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.12)

14:14:39:957  INFO 54375 --- [           main] c.h.s.i.MicroserviceIntegrationTest      : Starting MicroserviceIntegrationTest using Java 17.0.15 on WangJingkuo with PID 54375 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-service)
14:14:39:958 DEBUG 54375 --- [           main] c.h.s.i.MicroserviceIntegrationTest      : Running with Spring Boot v2.7.12, Spring v5.3.27
14:14:39:958  INFO 54375 --- [           main] c.h.s.i.MicroserviceIntegrationTest      : The following 1 profile is active: "test"
14:14:40:517 DEBUG 54375 --- [           main] o.s.c.openfeign.FeignClientsRegistrar$1  : Identified candidate component class: file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/classes/com/hmall/client/CartClient.class]
14:14:40:517 DEBUG 54375 --- [           main] o.s.c.openfeign.FeignClientsRegistrar$1  : Identified candidate component class: file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-service/target/classes/com/hmall/client/ItemClient.class]
14:14:40:693  INFO 54375 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
14:14:40:695  INFO 54375 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
14:14:40:712  INFO 54375 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
14:14:40:917 DEBUG 54375 --- [           main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [HMallApplication, accessorsProvider, addressController, addressMapper, addressServiceImpl, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.HMallApplication#MapperScannerRegistrar#0, com.hmall.client.CartClient, com.hmall.client.ItemClient, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, commonExceptionAdvice, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.HMallApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, helloController, hikariPoolDataSourceMetadataProvider, hm-cart-service.FeignClientSpecification, hm-product-service.FeignClientSpecification, hm.auth-com.hmall.config.AuthProperties, hm.jwt-com.hmall.config.JwtProperties, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonGeoModule, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, jwtTool, keyPair, keyValueMappingContext, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, lettuceClientResources, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcConfig, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, orderController, orderDetailMapper, orderDetailServiceImpl, orderLogisticsMapper, orderLogisticsServiceImpl, orderMapper, orderServiceImpl, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration, org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration, org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration, org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration, org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.netty.NettyAutoConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.data.web.config.ProjectingArgumentResolverRegistrar, org.springframework.data.web.config.SpringDataJacksonConfiguration, org.springframework.data.web.config.SpringDataWebConfiguration, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, pageableCustomizer, pageableResolver, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, passwordEncoder, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, payController, payOrderMapper, payOrderServiceImpl, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, projectingArgumentResolverBeanPostProcessor, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, reactiveRedisTemplate, reactiveStringRedisTemplate, redisConnectionFactory, redisConverter, redisCustomConversions, redisKeyValueAdapter, redisKeyValueTemplate, redisReferenceResolver, redisTemplate, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, securityConfig, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, sortCustomizer, sortResolver, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.netty-org.springframework.boot.autoconfigure.netty.NettyProperties, spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, stringRedisTemplate, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, userController, userMapper, userServiceImpl, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:14:40:931  INFO 54375 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f456dec9-0d8c-3e9b-945c-92ae68e97d80
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.4.3 
14:14:42:370  INFO 54375 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:14:42:568  INFO 54375 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:14:42:579  INFO 54375 --- [           main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:14:42:629  INFO 54375 --- [           main] o.s.c.openfeign.FeignClientFactoryBean   : For 'hm-product-service' URL not provided. Will try picking an instance via load-balancing.
14:14:42:765  INFO 54375 --- [           main] o.s.c.openfeign.FeignClientFactoryBean   : For 'hm-cart-service' URL not provided. Will try picking an instance via load-balancing.
14:14:43:887  INFO 54375 --- [           main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:14:44:409 DEBUG 54375 --- [           main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:14:44:410 DEBUG 54375 --- [           main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:14:44:410 DEBUG 54375 --- [           main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:14:44:455  INFO 54375 --- [           main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:14:44:458  INFO 54375 --- [           main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:14:44:483  INFO 54375 --- [           main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:14:44:656  INFO 54375 --- [           main] c.h.s.i.MicroserviceIntegrationTest      : Started MicroserviceIntegrationTest in 5.041 seconds (JVM running for 5.812)
14:14:44:686  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@787b7796, testMethod = testOrderServiceCreateOrderWithMicroservices@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:44:978  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-product-service
14:14:44:979  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-product-service
14:14:44:987  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@787b7796, testMethod = testOrderServiceCreateOrderWithMicroservices@MicroserviceIntegrationTest, testException = feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
  <testcase name="testServiceDiscoveryAndLoadBalancing" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.01">
    <error message="[503] during [GET] to [http://hm-product-service/items?ids=317578] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]" type="feign.FeignException$ServiceUnavailable">feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testServiceDiscoveryAndLoadBalancing(MicroserviceIntegrationTest.java:183)
</error>
    <system-out><![CDATA[14:14:44:993  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@1c10eb3a, testMethod = testServiceDiscoveryAndLoadBalancing@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:44:997  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-product-service
14:14:44:997  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-product-service
14:14:45:000  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@1c10eb3a, testMethod = testServiceDiscoveryAndLoadBalancing@MicroserviceIntegrationTest, testException = feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
  <testcase name="testItemClientDeductStock" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.008">
    <error message="[503] during [GET] to [http://hm-product-service/items?ids=317578&amp;ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]" type="feign.FeignException$ServiceUnavailable"><![CDATA[feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testItemClientDeductStock(MicroserviceIntegrationTest.java:83)
]]></error>
    <system-out><![CDATA[14:14:45:003  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@3b863b4, testMethod = testItemClientDeductStock@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:45:007  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-product-service
14:14:45:007  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-product-service
14:14:45:009  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@3b863b4, testMethod = testItemClientDeductStock@MicroserviceIntegrationTest, testException = feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
  <testcase name="testCartClientDeleteCartItemByIds" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.025">
    <failure message="Unexpected exception thrown: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&amp;ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service]" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Unexpected exception thrown: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testCartClientDeleteCartItemByIds(MicroserviceIntegrationTest.java:121)
Caused by: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.lambda$testCartClientDeleteCartItemByIds$7(MicroserviceIntegrationTest.java:121)
	at com.hmall.service.integration.MicroserviceIntegrationTest.testCartClientDeleteCartItemByIds(MicroserviceIntegrationTest.java:121)
]]></failure>
    <system-out><![CDATA[14:14:45:013  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@2524f9bf, testMethod = testCartClientDeleteCartItemByIds@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:45:029  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-cart-service
14:14:45:030  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-cart-service
14:14:45:034  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@2524f9bf, testMethod = testCartClientDeleteCartItemByIds@MicroserviceIntegrationTest, testException = org.opentest4j.AssertionFailedError: Unexpected exception thrown: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
  <testcase name="testItemClientQueryItemByIds" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.008">
    <error message="[503] during [GET] to [http://hm-product-service/items?ids=317578&amp;ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]" type="feign.FeignException$ServiceUnavailable"><![CDATA[feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testItemClientQueryItemByIds(MicroserviceIntegrationTest.java:52)
]]></error>
    <system-out><![CDATA[14:14:45:039  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@7d7d7b1a, testMethod = testItemClientQueryItemByIds@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:45:041  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-product-service
14:14:45:042  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-product-service
14:14:45:045  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@7d7d7b1a, testMethod = testItemClientQueryItemByIds@MicroserviceIntegrationTest, testException = feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
  <testcase name="testFeignClientErrorHandling" classname="com.hmall.service.integration.MicroserviceIntegrationTest" time="0.009">
    <error message="[503] during [GET] to [http://hm-product-service/items?ids=999999] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]" type="feign.FeignException$ServiceUnavailable">feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=999999] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testFeignClientErrorHandling(MicroserviceIntegrationTest.java:193)
</error>
    <system-out><![CDATA[14:14:45:048  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@67836d4, testMethod = testFeignClientErrorHandling@MicroserviceIntegrationTest, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]; transaction manager [org.springframework.jdbc.support.JdbcTransactionManager@a0e35c3]; rollback [true]
14:14:45:051  WARN 54375 --- [           main] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: hm-product-service
14:14:45:051  WARN 54375 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service hm-product-service
14:14:45:055  INFO 54375 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@4ce7fffa testClass = MicroserviceIntegrationTest, testInstance = com.hmall.service.integration.MicroserviceIntegrationTest@67836d4, testMethod = testFeignClientErrorHandling@MicroserviceIntegrationTest, testException = feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=999999] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service], mergedContextConfiguration = [WebMergedContextConfiguration@497486b3 testClass = MicroserviceIntegrationTest, locations = '{}', classes = '{class com.hmall.HMallApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@c430e6c, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@70cf32e3, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@239a307b, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@5b38c1ec, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@7e990ed7, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@4bb4de6a], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true, 'org.springframework.test.context.event.ApplicationEventsTestExecutionListener.recordApplicationEvents' -> false]]
]]></system-out>
  </testcase>
</testsuite>