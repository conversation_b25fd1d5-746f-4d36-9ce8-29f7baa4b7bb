-------------------------------------------------------------------------------
Test set: com.hmall.service.integration.MicroserviceIntegrationTest
-------------------------------------------------------------------------------
Tests run: 6, Failures: 1, Errors: 5, Skipped: 0, Time elapsed: 5.699 s <<< FAILURE! - in com.hmall.service.integration.MicroserviceIntegrationTest
testOrderServiceCreateOrderWithMicroservices  Time elapsed: 0.314 s  <<< ERROR!
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testOrderServiceCreateOrderWithMicroservices(MicroserviceIntegrationTest.java:139)

testServiceDiscoveryAndLoadBalancing  Time elapsed: 0.01 s  <<< ERROR!
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testServiceDiscoveryAndLoadBalancing(MicroserviceIntegrationTest.java:183)

testItemClientDeductStock  Time elapsed: 0.008 s  <<< ERROR!
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testItemClientDeductStock(MicroserviceIntegrationTest.java:83)

testCartClientDeleteCartItemByIds  Time elapsed: 0.025 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception thrown: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testCartClientDeleteCartItemByIds(MicroserviceIntegrationTest.java:121)
Caused by: feign.FeignException$ServiceUnavailable: [503] during [DELETE] to [http://hm-cart-service/carts?ids=317578&ids=317580] [CartClient#deleteCartItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-cart-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.lambda$testCartClientDeleteCartItemByIds$7(MicroserviceIntegrationTest.java:121)
	at com.hmall.service.integration.MicroserviceIntegrationTest.testCartClientDeleteCartItemByIds(MicroserviceIntegrationTest.java:121)

testItemClientQueryItemByIds  Time elapsed: 0.008 s  <<< ERROR!
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=317578&ids=317580] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testItemClientQueryItemByIds(MicroserviceIntegrationTest.java:52)

testFeignClientErrorHandling  Time elapsed: 0.009 s  <<< ERROR!
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://hm-product-service/items?ids=999999] [ItemClient#queryItemByIds(Collection)]: [Load balancer does not contain an instance for the service hm-product-service]
	at com.hmall.service.integration.MicroserviceIntegrationTest.testFeignClientErrorHandling(MicroserviceIntegrationTest.java:193)

