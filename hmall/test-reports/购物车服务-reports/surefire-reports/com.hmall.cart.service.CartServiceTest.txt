-------------------------------------------------------------------------------
Test set: com.hmall.cart.service.CartServiceTest
-------------------------------------------------------------------------------
Tests run: 6, Failures: 0, Errors: 6, Skipped: 0, Time elapsed: 3.572 s <<< FAILURE! - in com.hmall.cart.service.CartServiceTest
testAddSameItemTwice  Time elapsed: 0.011 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

testAddItem2Cart  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

testQueryMyCarts  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

testRemoveByItemIdsWithEmptyList  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

testRemoveByItemIds  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

testRemoveByItemIdsWithNonExistentIds  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartController' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/controller/CartController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'cartServiceImpl' defined in file [/home/<USER>/Jiangkuo/microServeice01/hmall/hm-cart-service/target/classes/com/hmall/cart/service/impl/CartServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]
Caused by: org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.hmall.cart.client.ItemClient' available: more than one 'primary' bean found among candidates: [com.hmall.cart.client.ItemClient, mockItemClient]

