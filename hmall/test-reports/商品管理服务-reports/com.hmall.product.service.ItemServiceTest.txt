-------------------------------------------------------------------------------
Test set: com.hmall.product.service.ItemServiceTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 0, Errors: 4, Skipped: 0, Time elapsed: 0.706 s <<< FAILURE! - in com.hmall.product.service.ItemServiceTest
testQueryItemByIdsWithNonExistentIds  Time elapsed: 0.013 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.cloud.commons.ConfigDataMissingEnvironmentPostProcessor$ImportException: No spring.config.import set

testQueryItemByIdsWithEmptyList  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.cloud.commons.ConfigDataMissingEnvironmentPostProcessor$ImportException: No spring.config.import set

testDeductStock  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.cloud.commons.ConfigDataMissingEnvironmentPostProcessor$ImportException: No spring.config.import set

testQueryItemByIds  Time elapsed: 0 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.cloud.commons.ConfigDataMissingEnvironmentPostProcessor$ImportException: No spring.config.import set

