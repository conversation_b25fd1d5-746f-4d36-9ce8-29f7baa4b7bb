13:46:24:503  INFO 36595 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 36595 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
13:46:24:503 DEBUG 36595 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
13:46:24:504  INFO 36595 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
13:46:25:410 DEBUG 36595 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiL<PERSON>ing<PERSON>rderReader, apiL<PERSON>ingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
13:46:25:423  INFO 36595 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
13:46:26:391  INFO 36595 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
13:46:26:556  INFO 36595 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
13:46:26:566  INFO 36595 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
13:46:27:179  INFO 36595 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
13:46:27:410 DEBUG 36595 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:46:27:410 DEBUG 36595 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:46:27:411 DEBUG 36595 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
13:46:27:508  INFO 36595 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
13:46:27:511  INFO 36595 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
13:46:27:543  INFO 36595 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
13:46:27:733  INFO 36595 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.657 seconds (JVM running for 4.397)
13:46:28:141 DEBUG 36595 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:46:28:203 DEBUG 36595 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
13:46:28:210 DEBUG 36595 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:46:28:218 DEBUG 36595 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:46:28:268  INFO 36595 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
13:46:28:271  INFO 36595 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
13:56:16:787  INFO 40976 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 40976 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
13:56:16:788 DEBUG 40976 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
13:56:16:789  INFO 40976 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
13:56:17:645 DEBUG 40976 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
13:56:17:660  INFO 40976 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
13:56:18:624  INFO 40976 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
13:56:18:790  INFO 40976 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
13:56:18:801  INFO 40976 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
13:56:19:436  INFO 40976 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
13:56:19:667 DEBUG 40976 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:56:19:668 DEBUG 40976 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:56:19:668 DEBUG 40976 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
13:56:19:738  INFO 40976 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
13:56:19:742  INFO 40976 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
13:56:19:792  INFO 40976 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
13:56:19:983  INFO 40976 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.539 seconds (JVM running for 4.273)
13:56:20:309 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:56:20:327 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
13:56:20:344 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
13:56:20:362 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
13:56:20:363 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
13:56:20:364 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
13:56:20:369 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:56:20:369 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:56:20:374 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:56:20:412  WARN 40976 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14b4d90b] Transaction not enabled
13:56:20:413 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
13:56:20:417 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
13:56:20:427 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
13:56:20:429 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:56:20:430 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:56:20:433 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:56:20:442 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:56:20:442 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:56:20:443 DEBUG 40976 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:56:20:491  INFO 40976 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
13:56:20:493  INFO 40976 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
13:59:52:658  INFO 43210 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 43210 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
13:59:52:659 DEBUG 43210 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
13:59:52:659  INFO 43210 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
13:59:53:528 DEBUG 43210 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
13:59:53:542  INFO 43210 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
13:59:54:437  INFO 43210 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
13:59:54:596  INFO 43210 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
13:59:54:605  INFO 43210 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
13:59:55:184  INFO 43210 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
13:59:55:439 DEBUG 43210 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:59:55:439 DEBUG 43210 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
13:59:55:440 DEBUG 43210 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
13:59:55:522  INFO 43210 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
13:59:55:526  INFO 43210 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
13:59:55:566  INFO 43210 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
13:59:55:760  INFO 43210 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.45 seconds (JVM running for 4.164)
13:59:56:126 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:59:56:143 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
13:59:56:161 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
13:59:56:180 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
13:59:56:181 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
13:59:56:182 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
13:59:56:188 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:59:56:189 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:59:56:194 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:59:56:239  WARN 43210 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22e1a1dc] Transaction not enabled
13:59:56:240 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
13:59:56:241 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
13:59:56:249 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
13:59:56:252 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:59:56:253 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:59:56:255 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:59:56:266 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
13:59:56:266 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
13:59:56:268 DEBUG 43210 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
13:59:56:316  INFO 43210 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
13:59:56:319  INFO 43210 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:03:43:213  INFO 45603 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 45603 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:03:43:214 DEBUG 45603 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:03:43:215  INFO 45603 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:03:44:072 DEBUG 45603 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:03:44:086  INFO 45603 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:03:45:041  INFO 45603 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:03:45:199  INFO 45603 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:03:45:207  INFO 45603 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:03:45:846  INFO 45603 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:03:46:080 DEBUG 45603 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:03:46:080 DEBUG 45603 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:03:46:080 DEBUG 45603 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:03:46:149  INFO 45603 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:03:46:153  INFO 45603 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:03:46:187  INFO 45603 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:03:46:379  INFO 45603 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.502 seconds (JVM running for 4.233)
14:03:46:741 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:03:46:761 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:03:46:780 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:03:46:801 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:03:46:802 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:03:46:803 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:03:46:808 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:03:46:808 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:03:46:813 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:03:46:850  WARN 45603 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14b4d90b] Transaction not enabled
14:03:46:851 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:03:46:854 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:03:46:862 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:03:46:865 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:03:46:865 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:03:46:869 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:03:46:878 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:03:46:879 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:03:46:880 DEBUG 45603 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:03:46:931  INFO 45603 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:03:46:935  INFO 45603 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:05:56:560  INFO 47306 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 47306 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:05:56:561 DEBUG 47306 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:05:56:561  INFO 47306 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:05:57:446 DEBUG 47306 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:05:57:460  INFO 47306 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:05:58:556  INFO 47306 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:05:58:732  INFO 47306 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:05:58:742  INFO 47306 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:05:59:384  INFO 47306 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:05:59:668 DEBUG 47306 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:05:59:668 DEBUG 47306 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:05:59:669 DEBUG 47306 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:05:59:743  INFO 47306 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:05:59:748  INFO 47306 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:05:59:785  INFO 47306 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:05:59:975  INFO 47306 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.758 seconds (JVM running for 4.49)
14:06:00:330 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:06:00:347 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:06:00:363 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:06:00:381 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:06:00:381 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:06:00:382 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:06:00:386 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:06:00:387 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:06:00:393 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:06:00:430  WARN 47306 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14b4d90b] Transaction not enabled
14:06:00:430 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:06:00:433 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:06:00:441 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:06:00:444 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:06:00:445 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:06:00:448 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:06:00:458 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:06:00:458 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:06:00:460 DEBUG 47306 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:06:00:519  INFO 47306 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:06:00:523  INFO 47306 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:07:41:303  INFO 48818 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 48818 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:07:41:304 DEBUG 48818 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:07:41:304  INFO 48818 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:07:42:149 DEBUG 48818 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:07:42:161  INFO 48818 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:07:43:117  INFO 48818 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:07:43:290  INFO 48818 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:07:43:300  INFO 48818 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:07:44:019  INFO 48818 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:07:44:270 DEBUG 48818 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:07:44:271 DEBUG 48818 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:07:44:271 DEBUG 48818 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:07:44:344  INFO 48818 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:07:44:350  INFO 48818 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:07:44:391  INFO 48818 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:07:44:600  INFO 48818 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.65 seconds (JVM running for 4.44)
14:07:45:003 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:07:45:026 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:07:45:047 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:07:45:068 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:07:45:069 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:07:45:070 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:07:45:076 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:07:45:076 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:07:45:081 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:07:45:120  WARN 48818 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14b4d90b] Transaction not enabled
14:07:45:120 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:07:45:123 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:07:45:132 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:07:45:136 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:07:45:136 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:07:45:139 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:07:45:150 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:07:45:150 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:07:45:152 DEBUG 48818 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:07:45:202  INFO 48818 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:07:45:205  INFO 48818 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:10:51:343  INFO 50984 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 50984 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:10:51:344 DEBUG 50984 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:10:51:345  INFO 50984 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:10:52:192 DEBUG 50984 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:10:52:205  INFO 50984 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:10:53:134  INFO 50984 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:10:53:298  INFO 50984 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:10:53:307  INFO 50984 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:10:53:931  INFO 50984 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:10:54:205 DEBUG 50984 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:10:54:206 DEBUG 50984 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:10:54:206 DEBUG 50984 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:10:54:277  INFO 50984 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:10:54:280  INFO 50984 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:10:54:316  INFO 50984 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:10:54:510  INFO 50984 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.516 seconds (JVM running for 4.233)
14:10:54:855 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:10:54:874 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:10:54:891 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:10:54:917 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:10:54:918 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:10:54:919 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:10:54:924 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:10:54:925 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:10:54:929 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:10:54:965  WARN 50984 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14b4d90b] Transaction not enabled
14:10:54:965 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:10:54:968 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:10:54:975 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:10:54:978 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:10:54:979 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:10:54:981 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:10:54:990 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:10:54:990 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:10:54:992 DEBUG 50984 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:10:55:045  INFO 50984 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:10:55:049  INFO 50984 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:12:28:556  INFO 52437 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 52437 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:12:28:556 DEBUG 52437 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:12:28:557  INFO 52437 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:12:29:453 DEBUG 52437 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:12:29:467  INFO 52437 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:12:30:497  INFO 52437 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:12:30:660  INFO 52437 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:12:30:669  INFO 52437 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:12:31:352  INFO 52437 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:12:31:598 DEBUG 52437 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:12:31:598 DEBUG 52437 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:12:31:599 DEBUG 52437 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:12:31:671  INFO 52437 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:12:31:674  INFO 52437 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:12:31:716  INFO 52437 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:12:31:911  INFO 52437 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.715 seconds (JVM running for 4.489)
14:12:32:279 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:12:32:296 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:12:32:314 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:12:32:332 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:12:32:333 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:12:32:334 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:12:32:339 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:12:32:339 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:12:32:344 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:12:32:380  WARN 52437 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19c06762] Transaction not enabled
14:12:32:380 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:12:32:384 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:12:32:392 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:12:32:396 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:12:32:397 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:12:32:400 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:12:32:412 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:12:32:412 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:12:32:414 DEBUG 52437 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:12:32:465  INFO 52437 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:12:32:471  INFO 52437 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:14:15:221  INFO 53836 --- [main] c.hmall.product.service.ItemServiceTest  : Starting ItemServiceTest using Java 17.0.15 on WangJingkuo with PID 53836 (started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:14:15:222 DEBUG 53836 --- [main] c.hmall.product.service.ItemServiceTest  : Running with Spring Boot v2.7.12, Spring v5.3.27
14:14:15:222  INFO 53836 --- [main] c.hmall.product.service.ItemServiceTest  : The following 1 profile is active: "test"
14:14:15:977 DEBUG 53836 --- [main] o.s.cloud.context.scope.GenericScope     : Generating bean factory id from names: [accessorsProvider, apacheHttpClientBuilder, apacheHttpClientFactory, apiDescriptionLookup, apiDescriptionReader, apiDocumentationScanner, apiListingBuilderPluginRegistry, apiListingOrderReader, apiListingReader, apiListingReferenceScanner, apiListingScanner, apiListingScannerPluginRegistry, apiModelBuilder, apiModelPropertyPropertyBuilder, apiModelReader, apiModelTypeNameProvider, apiOperationReader, apiResourceController, applicationAvailability, applicationTaskExecutor, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, blockingLoadBalancerClient, cachingModelDependencyProvider, cachingModelPropertiesProvider, cachingModelProvider, cachingOperationNameGenerator, cachingOperationReader, caffeineLoadBalancerCacheManager, characterEncodingFilter, classOrApiAnnotationResourceGrouping, cn.hutool.extra.spring.SpringUtil, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration$InetUtilsAutoConfig, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration, com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration$Knife4jEnhanceAutoConfiguration, com.hmall.common.config.JsonConfig, com.hmall.common.config.MyBatisConfig, com.hmall.product.ProductApplication#MapperScannerRegistrar#0, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, connManFactory, conventionErrorViewResolver, dataSource, dataSourceScriptDatabaseInitializer, default.com.hmall.product.ProductApplication.FeignClientSpecification, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, defaultModelDependencyProvider, defaultModelProvider, defaultOperationReader, defaultServletHandlerMapping, defaultTagsProvider, defaultValidator, defaultViewResolver, defaults, defaultsBindHandlerAdvisor, defaultsProviderPluginRegistry, descriptionResolver, dispatcherServlet, dispatcherServletRegistration, documentationPluginRegistry, documentationPluginsBootstrapper, documentationPluginsManager, dynamicParameterBuilderPlugin, dynamicResponseModelReader, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, expandedParameterBuilder, expandedParameterBuilderPluginRegistry, factoryMethodProvider, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, feignClient, feignContext, feignFeature, feignTargeter, fieldProvider, flashMapManager, formContentFilter, h2Console, handlerExceptionResolver, handlerFunctionAdapter, hikariPoolDataSourceMetadataProvider, httpRequestHandlerAdapter, identifierGenerator, inMemorySwaggerResourcesProvider, inetUtils, inetUtilsProperties, itemController, itemMapper, itemServiceImpl, jackson2ObjectMapperBuilderCustomizer, jacksonEnumTypeDeterminer, jacksonJsonViewProvider, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonSerializer, knife4j-com.github.xiaoymin.knife4j.spring.configuration.Knife4jProperties, knife4j.basic-com.github.xiaoymin.knife4j.spring.configuration.Knife4jHttpBasic, knife4j.openapi-com.github.xiaoymin.knife4j.spring.configuration.Knife4jInfoProperties, knife4j.openapi.basic-auth-com.github.xiaoymin.knife4j.spring.model.docket.Knife4jAuthInfoProperties, knife4j.setting-com.github.xiaoymin.knife4j.spring.configuration.Knife4jSetting, knife4jDocketAutoRegistry, licenseMapperImpl, lifecycleProcessor, loadBalancedAsyncRestTemplateInitializer, loadBalancedRestTemplateInitializerDeprecated, loadBalancerClientFactory, loadBalancerClientsDefaultsMappingsProvider, loadBalancerInterceptor, loadBalancerRequestFactory, loadBalancerServiceInstanceCookieTransformer, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, markdownResolver, mediaTypeReader, messageConverters, methodResolver, methodValidationPostProcessor, modelAttributeParameterExpander, modelBuilderPluginRegistry, modelMapperImpl, modelPropertyBuilderPluginRegistry, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, mybatisPlusInterceptor, namedParameterJdbcTemplate, objectMapperBeanPropertyNamingStrategy, objectMapperConfigurer, operationAuthReader, operationAuthorBuilderPlugin, operationBuilderPluginRegistry, operationDeprecatedReader, operationDynamicModelProvider, operationDynamicResponseModelProvider, operationHiddenReader, operationHttpMethodReader, operationIgnoreParameterPlugin, operationImplicitParameterReader, operationImplicitParametersReader, operationModelsProvider, operationModelsProviderPluginRegistry, operationNicknameIntoUniqueIdReader, operationNotesReader, operationOrderBuilderPlugin, operationParameterHeadersConditionReader, operationParameterReader, operationParameterRequestConditionReader, operationPathDecorator, operationPositionReader, operationResponseClassReader, operationSummaryReader, operationTagsReader, optimized, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor, org.springframework.boot.test.mock.mockito.MockitoPostProcessor$SpyPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterBuilderPluginRegistry, parameterDataTypeReader, parameterDefaultReader, parameterMapperImpl, parameterMultiplesReader, parameterNameReader, parameterNamesModule, parameterRequiredReader, parameterTypeReader, pathDecoratorRegistry, pathMappingDecorator, pathProvider, pathSanitizer, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, productApplication, propertyDiscriminatorBasedInheritancePlugin, propertySourcesPlaceholderConfigurer, queryStringUriTemplateDecorator, refreshEventListener, refreshScope, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceGroupCache, resourceGroupingStrategyRegistry, resourceHandlerMapping, responseMessagesReader, restTemplateBuilder, restTemplateBuilderConfigurer, restTemplateCustomizer, routerFunctionMapping, schemaPluginsManager, searchController, securityMapperImpl, server-org.springframework.boot.autoconfigure.web.ServerProperties, serviceModelToSwagger2MapperImpl, servletWebServerFactoryCustomizer, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.h2.console-org.springframework.boot.autoconfigure.h2.H2ConsoleProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, springFoxDocumentationBeanAfterPostProcessor, springfox.documentation.schema.configuration.ModelsConfiguration, springfox.documentation.spring.web.SpringfoxWebConfiguration, springfox.documentation.spring.web.SpringfoxWebMvcConfiguration, springfox.documentation.swagger.configuration.SwaggerCommonConfiguration, springfox.documentation.swagger2.configuration.Swagger2DocumentationWebMvcConfiguration, sqlSessionFactory, sqlSessionTemplate, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, swagger2ControllerMapping, swagger2Module, swaggerApiListingReader, swaggerExpandedParameterBuilder, swaggerMediaTypeReader, swaggerOperationModelsProvider, swaggerOperationResponseClassReader, swaggerOperationTagsReader, swaggerParameterDescriptionReader, swaggerResponseMessageReader, syntheticModelProviderPluginRegistry, taskExecutorBuilder, taskSchedulerBuilder, themeResolver, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, transactionAttributeSource, transactionInterceptor, transactionManager, transactionTemplate, typeNameExtractor, typeNameProviderPluginRegistry, typeResolver, vendorExtensionsMapperImpl, vendorExtensionsReader, viewControllerHandlerMapping, viewNameTranslator, viewProviderPluginRegistry, viewResolver, webMvcRequestHandlerProvider, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping, xForwarderHeadersTransformer, xmlModelPlugin, xmlPropertyPlugin, zoneConfig]
14:14:15:990  INFO 53836 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=f603b0c6-067e-39d7-9380-acdb44d199ba
14:14:16:918  INFO 53836 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
14:14:17:079  INFO 53836 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
14:14:17:089  INFO 53836 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
14:14:17:696  INFO 53836 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:14:17:922 DEBUG 53836 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:14:17:923 DEBUG 53836 --- [main] o.s.c.c.SpringBootVersionVerifier        : Version found in Boot manifest [2.7.12]
14:14:17:923 DEBUG 53836 --- [main] o.s.c.c.CompositeCompatibilityVerifier   : All conditions are passing
14:14:17:983  INFO 53836 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:14:17:986  INFO 53836 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:14:18:013  INFO 53836 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:14:18:196  INFO 53836 --- [main] c.hmall.product.service.ItemServiceTest  : Started ItemServiceTest in 3.3 seconds (JVM running for 3.958)
14:14:18:589 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:14:18:607 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 999999(Long), 888888(Long)
14:14:18:623 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:14:18:640 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( )
14:14:18:641 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 
14:14:18:642 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
14:14:18:648 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:14:18:649 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:14:18:654 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:14:18:694  WARN 53836 --- [main] c.h.p.service.impl.ItemServiceImpl       : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22e1a1dc] Transaction not enabled
14:14:18:695 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==>  Preparing: UPDATE item SET stock = stock - ? WHERE id = ?
14:14:18:697 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317578(Long)
14:14:18:706 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.updateStock      : ==> Parameters: 1(Integer), 317580(Long)
14:14:18:709 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:14:18:709 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:14:18:712 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:14:18:723 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
14:14:18:724 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
14:14:18:726 DEBUG 53836 --- [main] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
14:14:18:777  INFO 53836 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
14:14:18:779  INFO 53836 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
14:42:51:380  INFO 67896 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 67896 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:42:51:381 DEBUG 67896 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
14:42:51:382  INFO 67896 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
14:42:51:417  WARN 67896 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
14:42:52:024  INFO 67896 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
14:42:52:303  INFO 67896 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
14:42:52:309  INFO 67896 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
14:42:52:309  INFO 67896 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
14:42:52:393  INFO 67896 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
14:42:52:393  INFO 67896 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 973 ms
14:42:52:945  INFO 67896 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:42:53:064  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of 642311ae-23d4-4594-a581-e311dbb73481
14:42:53:064  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] RpcClient init label, labels = {module=naming, source=sdk}
14:42:53:065  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:42:53:066  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:42:53:066  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:42:53:066  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:42:53:205  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753944173084_172.18.0.1_45152
14:42:53:206  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:42:53:206  INFO 67896 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Notify connected event to listeners.
14:42:53:206  INFO 67896 --- [main] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$251/0x00007b6b8c343500
14:42:53:264  INFO 67896 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
14:42:53:282  INFO 67896 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
14:42:53:289  INFO 67896 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:42:53:292  INFO 67896 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:42:53:322  INFO 67896 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:42:53:429  INFO 67896 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 3.263 seconds (JVM running for 3.502)
14:42:53:432  INFO 67896 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
14:42:53:765  INFO 67896 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Receive server push request, request = NotifySubscriberRequest, requestId = 1
14:42:53:767  INFO 67896 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [642311ae-23d4-4594-a581-e311dbb73481] Ack server push request, request = NotifySubscriberRequest, requestId = 1
14:48:42:943  WARN 67896 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
14:48:42:943  WARN 67896 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
14:48:42:943  WARN 67896 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
14:48:42:943  WARN 67896 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
14:48:42:971  INFO 67896 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
14:48:42:975  INFO 67896 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
14:48:43:279  INFO 67896 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
14:48:43:279  INFO 67896 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@16280013[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:48:43:279  INFO 67896 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753944173084_172.18.0.1_45152
14:48:43:281  INFO 67896 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7b992337[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 116]
14:51:02:581  INFO 72951 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 72951 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:51:02:583 DEBUG 72951 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
14:51:02:584  INFO 72951 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
14:51:02:664  WARN 72951 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
14:51:03:910  INFO 72951 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
14:51:04:368  INFO 72951 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
14:51:04:379  INFO 72951 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
14:51:04:379  INFO 72951 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
14:51:04:495  INFO 72951 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
14:51:04:495  INFO 72951 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1827 ms
14:51:05:646  INFO 72951 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:51:05:865  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of ffc3c63c-3051-417d-83a0-93a20581bf55
14:51:05:865  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] RpcClient init label, labels = {module=naming, source=sdk}
14:51:05:866  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:51:05:867  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:51:05:867  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:51:05:868  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:51:06:017  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753944665892_172.18.0.1_47752
14:51:06:017  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:51:06:017  INFO 72951 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Notify connected event to listeners.
14:51:06:017  INFO 72951 --- [main] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$251/0x0000795988343500
14:51:06:058  INFO 72951 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
14:51:06:071  INFO 72951 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
14:51:06:078  INFO 72951 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:51:06:081  INFO 72951 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:51:06:106  INFO 72951 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:51:06:236  INFO 72951 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 5.684 seconds (JVM running for 6.177)
14:51:06:241  INFO 72951 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
14:51:06:610  INFO 72951 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Receive server push request, request = NotifySubscriberRequest, requestId = 3
14:51:06:613  INFO 72951 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [ffc3c63c-3051-417d-83a0-93a20581bf55] Ack server push request, request = NotifySubscriberRequest, requestId = 3
14:51:20:554  WARN 72951 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
14:51:20:555  WARN 72951 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
14:51:20:555  WARN 72951 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
14:51:20:555  WARN 72951 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
14:51:20:583  INFO 72951 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
14:51:20:588  INFO 72951 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
14:51:20:891  INFO 72951 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
14:51:20:891  INFO 72951 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7470ffcb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:51:20:891  INFO 72951 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753944665892_172.18.0.1_47752
14:51:20:894  INFO 72951 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18cfcd12[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 18]
14:51:31:607  INFO 73676 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 73676 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
14:51:31:607 DEBUG 73676 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
14:51:31:607  INFO 73676 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
14:51:31:643  WARN 73676 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
14:51:32:382  INFO 73676 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=4f57dcde-86e5-355f-98e2-ba4d1cd224f7
14:51:32:708  INFO 73676 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
14:51:32:717  INFO 73676 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
14:51:32:717  INFO 73676 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
14:51:32:814  INFO 73676 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
14:51:32:815  INFO 73676 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1170 ms
14:51:33:865  INFO 73676 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
14:51:34:118  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of d195db7e-448d-4e41-ad4e-deea203fb234
14:51:34:118  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] RpcClient init label, labels = {module=naming, source=sdk}
14:51:34:120  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:51:34:121  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:51:34:121  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:51:34:122  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:51:34:268  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753944694143_172.18.0.1_45650
14:51:34:268  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:51:34:268  INFO 73676 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Notify connected event to listeners.
14:51:34:268  INFO 73676 --- [main] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$315/0x00007efe9c3a0680
14:51:34:307  INFO 73676 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
14:51:34:320  INFO 73676 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
14:51:34:328  INFO 73676 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
14:51:34:333  INFO 73676 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
14:51:34:362  INFO 73676 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
14:51:34:493  INFO 73676 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 4.079 seconds (JVM running for 4.505)
14:51:34:498  INFO 73676 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
14:51:34:855  INFO 73676 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Receive server push request, request = NotifySubscriberRequest, requestId = 6
14:51:34:858  INFO 73676 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [d195db7e-448d-4e41-ad4e-deea203fb234] Ack server push request, request = NotifySubscriberRequest, requestId = 6
14:52:06:892  INFO 73676 --- [http-nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
14:52:06:892  INFO 73676 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
14:52:06:893  INFO 73676 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
15:02:33:719  WARN 73676 --- [http-nio-8081-exec-4] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'ids' for method parameter type List is not present]
15:03:56:279  INFO 73676 --- [http-nio-8081-exec-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
15:04:01:749 ERROR 73676 --- [http-nio-8081-exec-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Exception during pool initialization.

java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:903) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:453) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar:na]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.4.3.jar:3.4.3]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at jdk.proxy2/jdk.proxy2.$Proxy140.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at jdk.proxy2/jdk.proxy2.$Proxy91.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at jdk.proxy2/jdk.proxy2.$Proxy96.selectBatchIds(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.extension.service.IService.listByIds(IService.java:210) ~[mybatis-plus-extension-3.4.3.jar:3.4.3]
	at com.hmall.product.service.impl.ItemServiceImpl.queryItemByIds(ItemServiceImpl.java:42) ~[classes/:na]
	at com.hmall.product.service.impl.ItemServiceImpl$$FastClassBySpringCGLIB$$f7c7f66b.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.27.jar:5.3.27]
	at com.hmall.product.service.impl.ItemServiceImpl$$EnhancerBySpringCGLIB$$4a3a78c3.queryItemByIds(<generated>) ~[classes/:na]
	at com.hmall.product.controller.ItemController.queryItemByIds(ItemController.java:38) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.75.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.75.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
Caused by: com.mysql.cj.exceptions.CJException: Access denied for user 'root'@'localhost' (using password: YES)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:129) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:794) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:719) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:687) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:136) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.proceedHandshakeWithPluggableAuthentication(NativeAuthenticationProvider.java:469) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:174) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1350) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:847) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	... 99 common frames omitted

15:04:01:759 ERROR 73676 --- [http-nio-8081-exec-1] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
### The error may exist in com/hmall/product/mapper/ItemMapper.java (best guess)
### The error may involve com.hmall.product.mapper.ItemMapper.selectBatchIds
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.] with root cause

com.mysql.cj.exceptions.CJException: Access denied for user 'root'@'localhost' (using password: YES)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481) ~[na:na]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:129) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:794) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:719) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:687) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:136) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.proceedHandshakeWithPluggableAuthentication(NativeAuthenticationProvider.java:469) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:174) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1350) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:847) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:453) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.23.jar:8.0.23]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-4.0.3.jar:na]
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80) ~[spring-jdbc-5.3.27.jar:5.3.27]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.4.3.jar:3.4.3]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at jdk.proxy2/jdk.proxy2.$Proxy140.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at jdk.proxy2/jdk.proxy2.$Proxy91.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.3.jar:3.4.3]
	at jdk.proxy2/jdk.proxy2.$Proxy96.selectBatchIds(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.extension.service.IService.listByIds(IService.java:210) ~[mybatis-plus-extension-3.4.3.jar:3.4.3]
	at com.hmall.product.service.impl.ItemServiceImpl.queryItemByIds(ItemServiceImpl.java:42) ~[classes/:na]
	at com.hmall.product.service.impl.ItemServiceImpl$$FastClassBySpringCGLIB$$f7c7f66b.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.27.jar:5.3.27]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.27.jar:5.3.27]
	at com.hmall.product.service.impl.ItemServiceImpl$$EnhancerBySpringCGLIB$$4a3a78c3.queryItemByIds(<generated>) ~[classes/:na]
	at com.hmall.product.controller.ItemController.queryItemByIds(ItemController.java:38) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:569) ~[na:na]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.75.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.27.jar:5.3.27]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.75.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.27.jar:5.3.27]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.27.jar:5.3.27]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.75.jar:9.0.75]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]

15:11:24:655  WARN 73676 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
15:11:24:656  WARN 73676 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
15:11:24:656  WARN 73676 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
15:11:24:656  WARN 73676 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
15:11:24:689  INFO 73676 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
15:11:24:695  INFO 73676 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
15:11:25:002  INFO 73676 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
15:11:25:002  INFO 73676 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a16a882[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:11:25:002  INFO 73676 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753944694143_172.18.0.1_45650
15:11:25:005  INFO 73676 --- [nacos-grpc-client-executor-286] c.a.n.c.remote.client.grpc.GrpcClient    : [1753944694143_172.18.0.1_45650]Ignore complete event,isRunning:false,isAbandon=false
15:11:25:006  INFO 73676 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@459e5275[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 287]
15:17:20:350  INFO 90188 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 90188 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
15:17:20:351 DEBUG 90188 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
15:17:20:351  INFO 90188 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
15:17:20:405  WARN 90188 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
15:17:21:254  INFO 90188 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
15:17:21:574  INFO 90188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
15:17:21:581  INFO 90188 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
15:17:21:582  INFO 90188 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
15:17:21:687  INFO 90188 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
15:17:21:687  INFO 90188 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1279 ms
15:17:22:381  INFO 90188 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
15:17:22:509  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of 2bad7f83-eaff-4d1e-b213-23a4c277b3b9
15:17:22:509  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] RpcClient init label, labels = {module=naming, source=sdk}
15:17:22:511  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:17:22:511  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:17:22:512  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:17:22:513  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:17:22:653  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753946242530_172.18.0.1_45984
15:17:22:653  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:17:22:654  INFO 90188 --- [main] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$251/0x000077c6b0343500
15:17:22:654  INFO 90188 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Notify connected event to listeners.
15:17:22:699  INFO 90188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
15:17:22:713  INFO 90188 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
15:17:22:722  INFO 90188 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
15:17:22:725  INFO 90188 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
15:17:22:751  INFO 90188 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
15:17:22:871  INFO 90188 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 3.889 seconds (JVM running for 4.18)
15:17:22:876  INFO 90188 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
15:17:23:208  INFO 90188 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Receive server push request, request = NotifySubscriberRequest, requestId = 9
15:17:23:211  INFO 90188 --- [nacos-grpc-client-executor-10] com.alibaba.nacos.common.remote.client   : [2bad7f83-eaff-4d1e-b213-23a4c277b3b9] Ack server push request, request = NotifySubscriberRequest, requestId = 9
15:17:59:228  INFO 90188 --- [http-nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:59:228  INFO 90188 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
15:17:59:230  INFO 90188 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
15:17:59:311  INFO 90188 --- [http-nio-8081-exec-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
15:17:59:636  INFO 90188 --- [http-nio-8081-exec-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
15:17:59:644 DEBUG 90188 --- [http-nio-8081-exec-1] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? )
15:17:59:671 DEBUG 90188 --- [http-nio-8081-exec-1] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 0(Long)
15:17:59:714 DEBUG 90188 --- [http-nio-8081-exec-1] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
15:18:46:524  WARN 90188 --- [http-nio-8081-exec-2] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'ids' for method parameter type List is not present]
15:19:22:413 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.m.ItemMapper.selectPage_mpCount    : ==>  Preparing: SELECT COUNT(*) FROM item
15:19:22:413 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.m.ItemMapper.selectPage_mpCount    : ==> Parameters: 
15:19:22:483 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.m.ItemMapper.selectPage_mpCount    : <==      Total: 1
15:19:22:489 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectPage       : ==>  Preparing: SELECT id, name, price, stock, image, category, brand, spec, sold, comment_count, isAD, status, create_time, update_time, creater, updater FROM item ORDER BY update_time DESC LIMIT ?
15:19:22:489 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectPage       : ==> Parameters: 20(Long)
15:19:22:503 DEBUG 90188 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectPage       : <==      Total: 20
15:21:23:714 DEBUG 90188 --- [http-nio-8081-exec-7] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? )
15:21:23:715 DEBUG 90188 --- [http-nio-8081-exec-7] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long), 317580(Long)
15:21:23:717 DEBUG 90188 --- [http-nio-8081-exec-7] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 2
15:24:42:250 DEBUG 90188 --- [http-nio-8081-exec-9] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? , ? , ? )
15:24:42:251 DEBUG 90188 --- [http-nio-8081-exec-9] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 100000006163(Long), 317580(Long), 317578(Long)
15:24:42:255 DEBUG 90188 --- [http-nio-8081-exec-9] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 3
15:25:33:420 DEBUG 90188 --- [http-nio-8081-exec-10] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? )
15:25:33:420 DEBUG 90188 --- [http-nio-8081-exec-10] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 0(Long)
15:25:33:423 DEBUG 90188 --- [http-nio-8081-exec-10] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 0
15:29:29:931  WARN 90188 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
15:29:29:931  WARN 90188 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
15:29:29:932  WARN 90188 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
15:29:29:932  WARN 90188 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
15:29:29:959  INFO 90188 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
15:29:29:963  INFO 90188 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
15:29:30:266  INFO 90188 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
15:29:30:266  INFO 90188 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a7e3f5f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:29:30:266  INFO 90188 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753946242530_172.18.0.1_45984
15:29:30:267  INFO 90188 --- [nacos-grpc-client-executor-181] c.a.n.c.remote.client.grpc.GrpcClient    : [1753946242530_172.18.0.1_45984]Ignore complete event,isRunning:false,isAbandon=false
15:29:30:268  INFO 90188 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7b437778[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 182]
15:29:30:273  INFO 90188 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
15:29:30:282  INFO 90188 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
17:15:38:116  INFO 167633 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 167633 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
17:15:38:117 DEBUG 167633 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
17:15:38:117  INFO 167633 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
17:15:38:156  WARN 167633 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
17:15:38:852  INFO 167633 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
17:15:39:136  INFO 167633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
17:15:39:144  INFO 167633 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
17:15:39:144  INFO 167633 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
17:15:39:214  INFO 167633 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
17:15:39:215  INFO 167633 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1057 ms
17:15:39:928  INFO 167633 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
17:15:40:082  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of 6744d130-72d2-4887-8d33-439b6db8074d
17:15:40:082  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] RpcClient init label, labels = {module=naming, source=sdk}
17:15:40:084  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:15:40:084  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:15:40:084  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:15:40:085  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:15:40:229  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753953340106_172.18.0.1_46356
17:15:40:229  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:15:40:229  INFO 167633 --- [main] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$251/0x00007a920c3429e8
17:15:40:229  INFO 167633 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Notify connected event to listeners.
17:15:40:273  INFO 167633 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
17:15:40:285  INFO 167633 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
17:15:40:294  INFO 167633 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
17:15:40:297  INFO 167633 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
17:15:40:323  INFO 167633 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
17:15:40:424  INFO 167633 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 3.543 seconds (JVM running for 3.806)
17:15:40:427  INFO 167633 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
17:15:40:830  INFO 167633 --- [nacos-grpc-client-executor-9] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Receive server push request, request = NotifySubscriberRequest, requestId = 33
17:15:40:833  INFO 167633 --- [nacos-grpc-client-executor-9] com.alibaba.nacos.common.remote.client   : [6744d130-72d2-4887-8d33-439b6db8074d] Ack server push request, request = NotifySubscriberRequest, requestId = 33
17:17:07:262  INFO 167633 --- [http-nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
17:17:07:262  INFO 167633 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
17:17:07:263  INFO 167633 --- [http-nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
17:17:16:352  INFO 167633 --- [http-nio-8081-exec-3] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
17:17:16:548  INFO 167633 --- [http-nio-8081-exec-3] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
17:17:16:553 DEBUG 167633 --- [http-nio-8081-exec-3] c.h.p.mapper.ItemMapper.selectById       : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id=?
17:17:16:568 DEBUG 167633 --- [http-nio-8081-exec-3] c.h.p.mapper.ItemMapper.selectById       : ==> Parameters: 317578(Long)
17:17:16:585 DEBUG 167633 --- [http-nio-8081-exec-3] c.h.p.mapper.ItemMapper.selectById       : <==      Total: 1
17:23:22:080 DEBUG 167633 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectBatchIds   : ==>  Preparing: SELECT id,name,price,stock,image,category,brand,spec,sold,comment_count,isAD,status,create_time,update_time,creater,updater FROM item WHERE id IN ( ? )
17:23:22:081 DEBUG 167633 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectBatchIds   : ==> Parameters: 317578(Long)
17:23:22:084 DEBUG 167633 --- [http-nio-8081-exec-5] c.h.p.mapper.ItemMapper.selectBatchIds   : <==      Total: 1
17:29:05:033  WARN 167633 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
17:29:05:034  WARN 167633 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
17:29:05:035  WARN 167633 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
17:29:05:035  WARN 167633 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
17:29:05:074  INFO 167633 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
17:29:05:078  INFO 167633 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
17:29:05:380  INFO 167633 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
17:29:05:381  INFO 167633 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1c22c78c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:05:381  INFO 167633 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753953340106_172.18.0.1_46356
17:29:05:383  INFO 167633 --- [nacos-grpc-client-executor-179] c.a.n.c.remote.client.grpc.GrpcClient    : [1753953340106_172.18.0.1_46356]Ignore complete event,isRunning:false,isAbandon=false
17:29:05:383  INFO 167633 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@a13b50a[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 180]
17:29:05:386  INFO 167633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
17:29:05:397  INFO 167633 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
18:19:25:308  INFO 192895 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 192895 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
18:19:25:309 DEBUG 192895 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
18:19:25:309  INFO 192895 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
18:19:25:348  WARN 192895 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
18:19:26:087  INFO 192895 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
18:19:26:421  INFO 192895 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
18:19:26:427  INFO 192895 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
18:19:26:427  INFO 192895 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
18:19:26:503  INFO 192895 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
18:19:26:504  INFO 192895 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1154 ms
18:19:27:180  INFO 192895 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
18:19:27:342  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [RpcClientFactory] create a new rpc client of d337dd4c-4a18-4a5d-8d6a-e54ba487d170
18:19:27:342  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] RpcClient init label, labels = {module=naming, source=sdk}
18:19:27:343  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:19:27:344  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:19:27:344  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:19:27:344  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:19:27:486  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1753957167366_172.18.0.1_47872
18:19:27:487  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:19:27:487  INFO 192895 --- [com.alibaba.nacos.client.remote.worker] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Notify connected event to listeners.
18:19:27:487  INFO 192895 --- [main] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$251/0x000072f4bc343500
18:19:27:529  INFO 192895 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
18:19:27:541  INFO 192895 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP hm-product-service 10.255.255.254:8081 register finished
18:19:27:549  INFO 192895 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
18:19:27:551  INFO 192895 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
18:19:27:574  INFO 192895 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
18:19:27:707  INFO 192895 --- [main] com.hmall.product.ProductApplication     : Started ProductApplication in 3.814 seconds (JVM running for 4.103)
18:19:27:714  INFO 192895 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=hm-product-service.yaml, group=DEFAULT_GROUP
18:19:28:112  INFO 192895 --- [nacos-grpc-client-executor-9] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Receive server push request, request = NotifySubscriberRequest, requestId = 45
18:19:28:116  INFO 192895 --- [nacos-grpc-client-executor-9] com.alibaba.nacos.common.remote.client   : [d337dd4c-4a18-4a5d-8d6a-e54ba487d170] Ack server push request, request = NotifySubscriberRequest, requestId = 45
18:19:53:105  WARN 192895 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
18:19:53:106  WARN 192895 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
18:19:53:106  WARN 192895 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
18:19:53:106  WARN 192895 --- [Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
18:19:53:136  INFO 192895 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
18:19:53:140  INFO 192895 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
18:19:53:448  INFO 192895 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown rpc client, set status to shutdown
18:19:53:448  INFO 192895 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@161581a6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:19:53:448  INFO 192895 --- [SpringApplicationShutdownHook] com.alibaba.nacos.common.remote.client   : Close current connection 1753957167366_172.18.0.1_47872
18:19:53:451  INFO 192895 --- [SpringApplicationShutdownHook] c.a.n.c.remote.client.grpc.GrpcClient    : Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d5d78e0[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 21]
18:27:21:342  INFO 198804 --- [main] com.hmall.product.ProductApplication     : Starting ProductApplication using Java 17.0.15 on WangJingkuo with PID 198804 (/home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service/target/classes started by jiang in /home/<USER>/Jiangkuo/microServeice01/hmall/hm-product-service)
18:27:21:343 DEBUG 198804 --- [main] com.hmall.product.ProductApplication     : Running with Spring Boot v2.7.12, Spring v5.3.27
18:27:21:343  INFO 198804 --- [main] com.hmall.product.ProductApplication     : The following 1 profile is active: "local"
18:27:21:383  WARN 198804 --- [main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=hm-product-service.yaml, group=DEFAULT_GROUP] is empty
18:27:22:074  INFO 198804 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=3e0d7029-097e-3a25-9556-a5f5345f11ff
18:27:22:366  INFO 198804 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
18:27:22:372  INFO 198804 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
18:27:22:372  INFO 198804 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
18:27:22:443  INFO 198804 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
18:27:22:444  INFO 198804 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1059 ms
