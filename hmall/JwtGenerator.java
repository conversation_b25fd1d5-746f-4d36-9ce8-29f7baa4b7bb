import cn.hutool.jwt.JWT;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;

import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.Duration;
import java.util.Date;

public class JwtGenerator {
    public static void main(String[] args) {
        try {
            // 加载密钥库
            KeyStore keyStore = KeyStore.getInstance("JKS");
            keyStore.load(new FileInputStream("hm-common/src/main/resources/hmall.jks"), "hmall123".toCharArray());
            
            // 获取密钥对
            PrivateKey privateKey = (PrivateKey) keyStore.getKey("hmall", "hmall123".toCharArray());
            PublicKey publicKey = keyStore.getCertificate("hmall").getPublicKey();
            KeyPair keyPair = new KeyPair(publicKey, privateKey);
            
            // 创建JWT签名器
            JWTSigner jwtSigner = JWTSignerUtil.createSigner("rs256", keyPair);
            
            // 生成JWT token (用户ID为1，有效期30分钟)
            String token = JWT.create()
                    .setPayload("user", 1L)
                    .setExpiresAt(new Date(System.currentTimeMillis() + Duration.ofMinutes(30).toMillis()))
                    .setSigner(jwtSigner)
                    .sign();
            
            System.out.println("Generated JWT Token:");
            System.out.println(token);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
